#include "mcu_cmic_gd32f470vet6.h"

extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      RTC任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    // 更新RTC时间
    rtc_current_time_get(&rtc_initpara);
}

/**
 * @brief 设置RTC时间
 * @param time 时间结构体指针
 * @return 0-成功，-1-失败
 */
int rtc_set_time(const rtc_time_t *time)
{
    rtc_parameter_struct rtc_param;

    // 复制当前RTC参数
    rtc_param = rtc_initpara;

    // 设置新时间
    rtc_param.year = time->year;
    rtc_param.month = time->month;
    rtc_param.date = time->date;
    rtc_param.hour = time->hour;
    rtc_param.minute = time->minute;
    rtc_param.second = time->second;

    // 更新RTC
    if (ERROR == rtc_init(&rtc_param))
    {
        return -1;
    }

    // 更新全局参数
    rtc_initpara = rtc_param;

    return 0;
}

/**
 * @brief 获取RTC时间
 * @param time 时间结构体指针
 */
void rtc_get_time(rtc_time_t *time)
{
    rtc_current_time_get(&rtc_initpara);

    time->year = rtc_initpara.year;
    time->month = rtc_initpara.month;
    time->date = rtc_initpara.date;
    time->hour = rtc_initpara.hour;
    time->minute = rtc_initpara.minute;
    time->second = rtc_initpara.second;
}

/**
 * @brief 获取RTC时间字符串
 * @param buffer 缓冲区
 * @param size 缓冲区大小
 */
void rtc_get_time_string(char *buffer, size_t size)
{
    rtc_current_time_get(&rtc_initpara);

    snprintf(buffer, size, "20%02x-%02x-%02x %02x:%02x:%02x",
             rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
             rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}
