/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */

#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
  // 获取当前电压值
  float voltage = adc_get_voltage();

  // 获取当前采样频率
  adc_freq_t freq = adc_get_frequency();
  const char *freq_str = (freq == ADC_FREQ_10HZ) ? "10Hz" : "100Hz";

  // 获取当前工作模式
  const char *mode_str = get_work_mode_string();

  // 获取RTC时间
  extern rtc_parameter_struct rtc_initpara;
  rtc_current_time_get(&rtc_initpara);

  // 显示电压值（保留2位小数）
  oled_printf(0, 0, "Voltage: %.2fV", voltage);

  // 显示时间（格式：YYYY-MM-DD HH:MM:SS）
  oled_printf(0, 1, "20%02x-%02x-%02x %02x:%02x:%02x",
              rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
              rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

  // 显示采样频率和工作模式
  oled_printf(0, 2, "Freq:%s Mode:%s", freq_str, mode_str);

  // 显示ADC原始值
  oled_printf(0, 3, "ADC Raw: %d", adc_get_raw_value());
}

/* CUSTOM EDIT */
