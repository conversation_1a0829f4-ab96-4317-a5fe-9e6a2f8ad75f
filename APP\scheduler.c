/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */
#include "mcu_cmic_gd32f470vet6.h"

// ȫ�ֱ��������ڴ洢��������
uint8_t task_num;

typedef struct
{
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

// 静态任务数组，每个任务包含：任务函数、执行周期（毫秒）、上次运行时间（毫秒）
static task_t scheduler_task[] =
    {
        {led_task, 1, 0} // LED状态指示，1ms周期
        ,
        {adc_task, 10, 0} // ADC采样，10ms周期（支持100Hz最高频率）
        ,
        {oled_task, 100, 0} // OLED显示，100ms周期
        ,
        {btn_task, 5, 0} // 按键处理，5ms周期
        ,
        {uart_task, 5, 0} // 串口通信，5ms周期
        ,
        {rtc_task, 500, 0} // RTC时间更新，500ms周期
        ,
        {sd_task, 10, 0} // SD卡数据存储，10ms周期

};

/**
 * @brief ��������ʼ������
 * �������������Ԫ�ظ�������������洢�� task_num ��
 */
void scheduler_init(void)
{
    // �������������Ԫ�ظ�������������洢�� task_num ��
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief ���������к���
 * �����������飬����Ƿ���������Ҫִ�С������ǰʱ���Ѿ����������ִ�����ڣ���ִ�и����񲢸����ϴ�����ʱ��
 */
void scheduler_run(void)
{
    // �������������е���������
    for (uint8_t i = 0; i < task_num; i++)
    {
        // ��ȡ��ǰ��ϵͳʱ�䣨���룩
        uint32_t now_time = get_system_ms();

        // ��鵱ǰʱ���Ƿ�ﵽ�����ִ��ʱ��
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // ����������ϴ�����ʱ��Ϊ��ǰʱ��
            scheduler_task[i].last_run = now_time;

            // ִ��������
            scheduler_task[i].task_func();
        }
    }
}
