#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

    // ADC报警状态枚举
    typedef enum
    {
        ADC_ALARM_NONE = 0,    // 无报警
        ADC_ALARM_ACTIVE = 1,  // 报警中
        ADC_ALARM_RECORDED = 2 // 已记录
    } adc_alarm_state_t;

    // 函数声明
    void adc_task(void);
    float adc_get_voltage(void);
    float adc_get_threshold(void);
    void adc_set_threshold(float threshold);
    void adc_threshold_increase(void);
    void adc_threshold_decrease(void);
    adc_alarm_state_t adc_get_alarm_state(void);
    uint8_t adc_is_alarm_active(void);
    void adc_init_system(void);

#ifdef __cplusplus
}
#endif

#endif /* __ADC_APP_H_ */
