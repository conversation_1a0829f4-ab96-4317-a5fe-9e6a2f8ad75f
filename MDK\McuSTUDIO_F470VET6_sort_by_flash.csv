File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.608398%,9815,96,9264,551,0,96
sdio_sdcard.o,12.886530%,6797,61,6792,0,5,56
ff.o,10.201915%,5381,6,5366,15,0,6
oled.o,6.271685%,3308,22,1214,2072,22,0
sd_app.o,3.753910%,1980,1413,1424,556,0,1413
btod.o,3.670490%,1936,0,1936,0,0,0
ebtn.o,3.181344%,1678,60,1678,0,0,60
mcu_cmic_gd32f470vet6.o,2.976585%,1570,576,1568,0,2,574
gd25qxx.o,2.375581%,1253,0,938,315,0,0
_printf_fp_dec.o,1.994502%,1052,0,1052,0,0,0
usart_app.o,1.761304%,929,519,822,107,0,519
perf_counter.o,1.721490%,908,60,896,4,8,52
_scanf.o,1.675988%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.531899%,808,0,800,8,0,0
_printf_fp_hex.o,1.520523%,802,0,764,38,0,0
gd32f4xx_dma.o,1.209593%,638,0,638,0,0,0
gd32f4xx_sdio.o,1.152716%,608,0,608,0,0,0
system_gd32f4xx.o,1.046545%,552,4,548,0,4,0
btn_app.o,1.042753%,550,197,314,40,196,1
gd32f4xx_timer.o,1.020002%,538,0,538,0,0,0
gd32f4xx_usart.o,1.008626%,532,0,532,0,0,0
gd32f4xx_adc.o,0.985875%,520,0,520,0,0,0
startup_gd32f450_470.o,0.932790%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.826619%,436,0,436,0,0,0
gd32f4xx_i2c.o,0.822827%,434,0,434,0,0,0
__printf_flags_ss_wp.o,0.775429%,409,0,392,17,0,0
bigflt0.o,0.712864%,376,0,228,148,0,0
_scanf_int.o,0.629444%,332,0,332,0,0,0
lc_ctype_c.o,0.599109%,316,0,44,272,0,0
oled_app.o,0.564982%,298,0,298,0,0,0
gd32f4xx_gpio.o,0.534648%,282,0,282,0,0,0
fz_wm.l,0.515689%,272,0,272,0,0,0
lludivv7m.o,0.455019%,240,0,240,0,0,0
diskio.o,0.447436%,236,0,236,0,0,0
rtc_app.o,0.443644%,234,0,234,0,0,0
led_app.o,0.388662%,205,7,186,12,7,0
_printf_wctomb.o,0.371599%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.356432%,188,0,148,40,0,0
gd32f4xx_dac.o,0.345056%,182,0,182,0,0,0
_printf_intcommon.o,0.337473%,178,0,178,0,0,0
scheduler.o,0.326097%,172,85,88,0,84,1
gd32f4xx_misc.o,0.307138%,162,0,162,0,0,0
adc_app.o,0.307138%,162,9,162,0,0,9
gd32f4xx_it.o,0.303346%,160,0,160,0,0,0
fnaninf.o,0.265428%,140,0,140,0,0,0
rt_memcpy_v6.o,0.261636%,138,0,138,0,0,0
lludiv10.o,0.261636%,138,0,138,0,0,0
_printf_fp_infnan.o,0.242677%,128,0,128,0,0,0
strcmpv7em.o,0.235093%,124,0,124,0,0,0
_printf_longlong_dec.o,0.235093%,124,0,124,0,0,0
perfc_port_default.o,0.231302%,122,0,122,0,0,0
_printf_dec.o,0.227510%,120,0,120,0,0,0
systick.o,0.223718%,118,4,118,0,0,4
llsdiv.o,0.219926%,116,0,116,0,0,0
_printf_oct_int_ll.o,0.212342%,112,0,112,0,0,0
gd32f4xx_spi.o,0.193383%,102,0,102,0,0,0
rt_memcpy_w.o,0.189591%,100,0,100,0,0,0
__scatter.o,0.178216%,94,0,94,0,0,0
__dczerorl2.o,0.170632%,90,0,90,0,0,0
memcmp.o,0.166840%,88,0,88,0,0,0
f2d.o,0.163049%,86,0,86,0,0,0
_printf_str.o,0.155465%,82,0,82,0,0,0
main.o,0.155465%,82,0,82,0,0,0
rt_memclr_w.o,0.147881%,78,0,78,0,0,0
_printf_pad.o,0.147881%,78,0,78,0,0,0
sys_stackheap_outer.o,0.140298%,74,0,74,0,0,0
lc_numeric_c.o,0.136506%,72,0,44,28,0,0
_c16rtomb.o,0.136506%,72,0,72,0,0,0
rt_memclr.o,0.128922%,68,0,68,0,0,0
_sgetc.o,0.121339%,64,0,64,0,0,0
__2snprintf.o,0.121339%,64,0,64,0,0,0
strlen.o,0.117547%,62,0,62,0,0,0
vsnprintf.o,0.113755%,60,0,60,0,0,0
__0sscanf.o,0.113755%,60,0,60,0,0,0
m_wm.l,0.091004%,48,0,48,0,0,0
fpclassify.o,0.091004%,48,0,48,0,0,0
_printf_char_common.o,0.091004%,48,0,48,0,0,0
scanf_char.o,0.083420%,44,0,44,0,0,0
init_aeabi.o,0.083420%,44,0,44,0,0,0
_printf_wchar.o,0.083420%,44,0,44,0,0,0
_printf_char.o,0.083420%,44,0,44,0,0,0
_printf_charcount.o,0.075837%,40,0,40,0,0,0
libinit2.o,0.072045%,38,0,38,0,0,0
_printf_truncate.o,0.068253%,36,0,36,0,0,0
_chval.o,0.053086%,28,0,28,0,0,0
__scatter_zi.o,0.053086%,28,0,28,0,0,0
systick_wrapper_gnu.o,0.053086%,28,0,28,0,0,0
fpinit.o,0.049294%,26,0,26,0,0,0
isspace.o,0.034126%,18,0,18,0,0,0
exit.o,0.034126%,18,0,18,0,0,0
gd32f4xx_pmu.o,0.034126%,18,0,18,0,0,0
rt_ctype_table.o,0.030335%,16,0,16,0,0,0
_snputc.o,0.030335%,16,0,16,0,0,0
__printf_wp.o,0.026543%,14,0,14,0,0,0
dretinf.o,0.022751%,12,0,12,0,0,0
sys_exit.o,0.022751%,12,0,12,0,0,0
__rtentry2.o,0.022751%,12,0,12,0,0,0
rtexit2.o,0.018959%,10,0,10,0,0,0
_printf_ll.o,0.018959%,10,0,10,0,0,0
_printf_l.o,0.018959%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015167%,8,0,8,0,0,0
libspace.o,0.015167%,8,96,8,0,0,96
__main.o,0.015167%,8,0,8,0,0,0
heapauxi.o,0.011375%,6,0,6,0,0,0
_printf_x.o,0.011375%,6,0,6,0,0,0
_printf_u.o,0.011375%,6,0,6,0,0,0
_printf_s.o,0.011375%,6,0,6,0,0,0
_printf_p.o,0.011375%,6,0,6,0,0,0
_printf_o.o,0.011375%,6,0,6,0,0,0
_printf_n.o,0.011375%,6,0,6,0,0,0
_printf_ls.o,0.011375%,6,0,6,0,0,0
_printf_llx.o,0.011375%,6,0,6,0,0,0
_printf_llu.o,0.011375%,6,0,6,0,0,0
_printf_llo.o,0.011375%,6,0,6,0,0,0
_printf_lli.o,0.011375%,6,0,6,0,0,0
_printf_lld.o,0.011375%,6,0,6,0,0,0
_printf_lc.o,0.011375%,6,0,6,0,0,0
_printf_i.o,0.011375%,6,0,6,0,0,0
_printf_g.o,0.011375%,6,0,6,0,0,0
_printf_f.o,0.011375%,6,0,6,0,0,0
_printf_e.o,0.011375%,6,0,6,0,0,0
_printf_d.o,0.011375%,6,0,6,0,0,0
_printf_c.o,0.011375%,6,0,6,0,0,0
_printf_a.o,0.011375%,6,0,6,0,0,0
__rtentry4.o,0.011375%,6,0,6,0,0,0
printf2.o,0.007584%,4,0,4,0,0,0
printf1.o,0.007584%,4,0,4,0,0,0
_printf_percent_end.o,0.007584%,4,0,4,0,0,0
use_no_semi.o,0.003792%,2,0,2,0,0,0
rtexit.o,0.003792%,2,0,2,0,0,0
libshutdown2.o,0.003792%,2,0,2,0,0,0
libshutdown.o,0.003792%,2,0,2,0,0,0
libinit.o,0.003792%,2,0,2,0,0,0
