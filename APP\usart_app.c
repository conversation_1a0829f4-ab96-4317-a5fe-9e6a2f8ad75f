/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */
#include "mcu_cmic_gd32f470vet6.h"

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // ��ʼ���ɱ�����б�
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for (tx_count = 0; tx_count < len; tx_count++)
    {
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while (RESET == usart_flag_get(usart_periph, USART_FLAG_TBE))
            ;
    }

    return len;
}

// 状态发送计数器
static uint32_t status_send_counter = 0;

/**
 * @brief 发送系统状态到串口
 */
void uart_send_status(void)
{
    float voltage = adc_get_voltage();
    const char *mode_str = get_work_mode_string();
    adc_freq_t freq = adc_get_frequency();
    const char *freq_str = (freq == ADC_FREQ_10HZ) ? "10Hz" : "100Hz";

    char time_str[32];
    rtc_get_time_string(time_str, sizeof(time_str));

    my_printf(DEBUG_USART, "STATUS: Voltage=%.2fV, Mode=%s, Freq=%s, Time=%s\r\n",
              voltage, mode_str, freq_str, time_str);
}

/**
 * @brief 解析TIME命令
 * @param cmd 命令字符串
 * @return 0-成功，-1-失败
 */
int uart_parse_time_command(const char *cmd)
{
    rtc_time_t new_time;
    int year, month, date, hour, minute, second;

    // 解析TIME=YYYY-MM-DD HH:MM:SS格式
    if (sscanf(cmd, "TIME=%d-%d-%d %d:%d:%d",
               &year, &month, &date, &hour, &minute, &second) == 6)
    {

        // 验证时间范围
        if (year >= 2000 && year <= 2099 &&
            month >= 1 && month <= 12 &&
            date >= 1 && date <= 31 &&
            hour >= 0 && hour <= 23 &&
            minute >= 0 && minute <= 59 &&
            second >= 0 && second <= 59)
        {

            // 转换为BCD格式
            new_time.year = (year - 2000) / 10 * 16 + (year - 2000) % 10;
            new_time.month = month / 10 * 16 + month % 10;
            new_time.date = date / 10 * 16 + date % 10;
            new_time.hour = hour / 10 * 16 + hour % 10;
            new_time.minute = minute / 10 * 16 + minute % 10;
            new_time.second = second / 10 * 16 + second % 10;

            // 设置RTC时间
            if (rtc_set_time(&new_time) == 0)
            {
                my_printf(DEBUG_USART, "TIME SET OK: %04d-%02d-%02d %02d:%02d:%02d\r\n",
                          year, month, date, hour, minute, second);
                return 0;
            }
            else
            {
                my_printf(DEBUG_USART, "TIME SET FAILED\r\n");
                return -1;
            }
        }
        else
        {
            my_printf(DEBUG_USART, "TIME FORMAT ERROR: Invalid range\r\n");
            return -1;
        }
    }
    else
    {
        my_printf(DEBUG_USART, "TIME FORMAT ERROR: Use TIME=YYYY-MM-DD HH:MM:SS\r\n");
        return -1;
    }
}

void uart_task(void)
{
    // 处理接收到的命令
    if (rx_flag)
    {
        // 检查是否是TIME命令
        if (strncmp((char *)uart_dma_buffer, "TIME=", 5) == 0)
        {
            uart_parse_time_command((char *)uart_dma_buffer);
        }
        else
        {
            // 回显接收到的数据
            my_printf(DEBUG_USART, "RECV: %s", uart_dma_buffer);
        }

        // 清除接收标志和缓冲区
        rx_flag = 0;
        memset(uart_dma_buffer, 0, 512);
    }

    // 定期发送状态信息（每5秒发送一次）
    status_send_counter++;
    if (status_send_counter >= 1000)
    { // 5ms * 1000 = 5秒
        uart_send_status();
        status_send_counter = 0;
    }
}
