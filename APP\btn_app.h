#ifndef __BTN_APP_H
#define __BTN_APP_H

#include "stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

    // 工作模式枚举
    typedef enum
    {
        WORK_MODE_MONITOR = 0,  // 监控模式
        WORK_MODE_STORAGE = 1,  // 数据存储模式
        WORK_MODE_CALIBRATE = 2 // 校准模式
    } work_mode_t;

    // 函数声明
    void app_btn_init(void);
    void btn_task(void);
    work_mode_t get_work_mode(void);
    void set_work_mode(work_mode_t mode);
    const char *get_work_mode_string(void);

#ifdef __cplusplus
}
#endif

#endif /* __BTN_APP_H */
