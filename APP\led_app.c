/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */
#include "mcu_cmic_gd32f470vet6.h"

uint8_t ucLed[6] = {1, 0, 1, 0, 1, 0}; // LED ״̬����

/**
 * @brief ��ʾ��ر�Led
 *
 *
 * @param ucLed Led���ݴ�������
 */
void led_disp(uint8_t *ucLed)
{
    // ���ڼ�¼��ǰ LED ״̬����ʱ����
    uint8_t temp = 0x00;
    // ��¼֮ǰ LED ״̬�ı����������ж��Ƿ���Ҫ������ʾ
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        // ��LED״̬���ϵ�temp�����У���������Ƚ�
        if (ucLed[i])
            temp |= (1 << i); // ����iλ��1
    }

    // ������ǰ״̬��֮ǰ״̬��ͬ��ʱ�򣬲Ÿ�����ʾ
    if (temp_old != temp)
    {
        // ����GPIO��ʼ����������ö�Ӧ����
        LED1_SET(temp & 0x01);
        LED1_SET(temp & 0x02);
        LED1_SET(temp & 0x04);
        LED1_SET(temp & 0x08);
        LED1_SET(temp & 0x10);
        LED1_SET(temp & 0x20);

        // ���¾�״̬
        temp_old = temp;
    }
}

/**
 * @brief 设置工作模式LED指示
 * 使用LED1、LED2、LED3来指示当前工作模式
 */
void led_set_work_mode_indication(void)
{
    work_mode_t mode = get_work_mode();

    // 清除所有LED状态
    ucLed[0] = 0; // LED1
    ucLed[1] = 0; // LED2
    ucLed[2] = 0; // LED3

    // 根据工作模式设置LED状态
    switch (mode)
    {
    case WORK_MODE_MONITOR:
        ucLed[0] = 1; // LED1亮 - 监控模式
        break;
    case WORK_MODE_STORAGE:
        ucLed[1] = 1; // LED2亮 - 数据存储模式
        break;
    case WORK_MODE_CALIBRATE:
        ucLed[2] = 1; // LED3亮 - 校准模式
        break;
    default:
        break;
    }
}

/**
 * @brief LED 显示任务函数
 *
 * 每次调用该函数时，LED 灯根据 ucLed 数组中的值，决定是开启还是关闭
 */
void led_task(void)
{
    // 更新工作模式LED指示
    led_set_work_mode_indication();

    // 显示LED状态
    led_disp(ucLed);
}
