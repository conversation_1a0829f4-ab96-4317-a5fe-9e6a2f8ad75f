#ifndef __RTC_APP_H_
#define __RTC_APP_H_

#include "stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

    // RTC时间结构体
    typedef struct
    {
        uint8_t year;
        uint8_t month;
        uint8_t date;
        uint8_t hour;
        uint8_t minute;
        uint8_t second;
    } rtc_time_t;

    // 函数声明
    void rtc_task(void);
    int rtc_set_time(const rtc_time_t *time);
    void rtc_get_time(rtc_time_t *time);
    void rtc_get_time_string(char *buffer, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H_ */
